#!/usr/bin/env python3
"""
Test script to verify the local Llama model filtering functionality.
"""

import json
from main import get_filtering_decision

def test_filtering():
    """Test the filtering function with a sample data point."""
    
    # Sample test data point
    test_example = {
        "question": "Which is harder, a diamond or a piece of wood?",
        "choice_1": "Diamond",
        "choice_2": "Wood", 
        "choice_3": "They are equally hard",
        "choice_4": "Cannot be determined",
        "gt": "Diamond"
    }
    
    print("Testing local Llama model filtering...")
    print(f"Test example: {json.dumps(test_example, indent=2)}")
    print("\nCalling filtering function...")
    
    try:
        result = get_filtering_decision(test_example, "test_dataset")
        print(f"\nFiltering result: {json.dumps(result, indent=2)}")
        
        if result.get("keep") is not None and result.get("reason"):
            print("✅ Filtering test successful!")
            return True
        else:
            print("❌ Filtering test failed - invalid response format")
            return False
            
    except Exception as e:
        print(f"❌ Filtering test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_filtering()
    if success:
        print("\n🎉 Local model filtering is working correctly!")
    else:
        print("\n💥 Local model filtering needs debugging.")
