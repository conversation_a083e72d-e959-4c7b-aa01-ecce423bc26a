#!/usr/bin/env python3
"""
Real-time progress monitoring script for the filtering pipeline.
Run this in a separate terminal to monitor progress.
"""

import os
import time
import json
from datetime import datetime

def monitor_filtering_progress():
    """Monitor the progress of filtering by watching output files."""
    
    # Files to monitor
    output_files = [
        "filtered_newton_explicit.jsonl",
        "filtered_newton_implicit.jsonl", 
        "filtered_physical_reasoning.jsonl",
        "filtered_prost.jsonl",
        "filtered_piqa.jsonl"
    ]
    
    print("🔍 Real-time Filtering Progress Monitor")
    print("=" * 60)
    print(f"⏰ Started monitoring at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Track previous counts
    prev_counts = {file: 0 for file in output_files}
    
    try:
        while True:
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\r🕐 {current_time} | ", end="")
            
            status_parts = []
            total_processed = 0
            
            for file in output_files:
                if os.path.exists(file):
                    try:
                        with open(file, 'r') as f:
                            count = sum(1 for line in f if line.strip())
                        
                        # Check if count increased
                        if count > prev_counts[file]:
                            delta = count - prev_counts[file]
                            status_parts.append(f"{file.split('_')[1]}: {count} (+{delta})")
                            prev_counts[file] = count
                        else:
                            status_parts.append(f"{file.split('_')[1]}: {count}")
                        
                        total_processed += count
                    except:
                        status_parts.append(f"{file.split('_')[1]}: ERROR")
                else:
                    status_parts.append(f"{file.split('_')[1]}: 0")
            
            status = " | ".join(status_parts)
            print(f"{status} | Total: {total_processed}", end="", flush=True)
            
            time.sleep(5)  # Update every 5 seconds
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 Monitoring stopped at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📊 Final counts:")
        for file in output_files:
            if os.path.exists(file):
                try:
                    with open(file, 'r') as f:
                        count = sum(1 for line in f if line.strip())
                    print(f"  {file}: {count} examples")
                except:
                    print(f"  {file}: ERROR reading file")
            else:
                print(f"  {file}: Not created yet")

def show_sample_from_file(filename):
    """Show a sample from a filtered file."""
    if os.path.exists(filename):
        try:
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            if lines:
                print(f"\n📄 Sample from {filename}:")
                sample = json.loads(lines[0])
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"  {key}: {value[:100]}...")
                    else:
                        print(f"  {key}: {value}")
            else:
                print(f"📄 {filename} is empty")
        except Exception as e:
            print(f"❌ Error reading {filename}: {e}")
    else:
        print(f"📄 {filename} does not exist yet")

def main():
    print("🚀 Filtering Progress Monitor")
    print("Run this while your main filtering pipeline is running")
    print("Press Ctrl+C to stop monitoring")
    print()
    
    # Show initial state
    print("📋 Initial state:")
    output_files = [
        "filtered_newton_explicit.jsonl",
        "filtered_newton_implicit.jsonl", 
        "filtered_physical_reasoning.jsonl",
        "filtered_prost.jsonl",
        "filtered_piqa.jsonl"
    ]
    
    for file in output_files:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    count = sum(1 for line in f if line.strip())
                print(f"  {file}: {count} examples")
            except:
                print(f"  {file}: ERROR")
        else:
            print(f"  {file}: Not created")
    
    print("\n" + "="*60)
    
    # Start monitoring
    monitor_filtering_progress()

if __name__ == "__main__":
    main()
