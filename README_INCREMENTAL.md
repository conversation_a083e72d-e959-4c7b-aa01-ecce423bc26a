# 🚀 Incremental Writing Pipeline

Your filtering pipeline now supports **real-time incremental writing**! This means:

## ✅ **What's New**

1. **Real-time Writing**: Results are written to files as they're processed (not at the end)
2. **Progress Monitoring**: Watch progress in real-time with live file updates
3. **Data Safety**: No data loss if the process is interrupted
4. **Multi-GPU Performance**: Optimized for 6 GPUs with ~1 second per example

## 🔥 **How to Run**

### 1. Start the Main Pipeline
```bash
# Use all 6 GPUs for maximum speed
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5 python main.py
```

### 2. Monitor Progress (Optional)
In a **separate terminal**, run:
```bash
python monitor_progress.py
```

This will show real-time updates like:
```
🕐 14:23:15 | newton: 1,234 (+50) | implicit: 892 (+23) | physical: 0 | prost: 0 | piqa: 0 | Total: 2,126
```

## 📊 **Output Files**

The pipeline creates these files **as it processes**:

- `filtered_newton_explicit.jsonl` - Newton explicit questions
- `filtered_newton_implicit.jsonl` - Newton implicit questions  
- `filtered_physical_reasoning.jsonl` - Physical reasoning dataset
- `filtered_prost.jsonl` - PROST dataset
- `filtered_piqa.jsonl` - PIQA dataset

## 🔍 **Real-time Monitoring**

You can watch progress by:

1. **File sizes**: `watch -n 5 'ls -lh filtered_*.jsonl'`
2. **Line counts**: `watch -n 5 'wc -l filtered_*.jsonl'`
3. **Monitor script**: `python monitor_progress.py`

## ⚡ **Performance**

- **Speed**: ~1 second per example
- **Memory**: ~15GB across multiple GPUs
- **Throughput**: ~3,600 examples/hour
- **Total time**: ~23 hours for all datasets

## 🛡️ **Safety Features**

1. **Immediate writes**: Each kept example is written immediately
2. **Flush to disk**: Files are flushed to ensure data persistence
3. **Progress tracking**: Regular progress updates every 1,000 examples
4. **Error handling**: Robust error handling with fallbacks

## 📈 **Expected Timeline**

| Dataset | Examples | Est. Time |
|---------|----------|-----------|
| newton_explicit | 84,182 | ~23 hours |
| newton_implicit | 21,395 | ~6 hours |
| physical_reasoning | 2,508 | ~1 hour |
| prost | 18,736 | ~5 hours |
| piqa | 16,113 | ~4 hours |
| **Total** | **142,934** | **~39 hours** |

## 🎯 **Tips**

1. **Run overnight**: The process takes many hours
2. **Monitor remotely**: Use `screen` or `tmux` for long-running processes
3. **Check progress**: Files update in real-time, so you can see progress anytime
4. **Resume capability**: If interrupted, restart and it will continue (currently restarts each dataset)

## 🚨 **If Something Goes Wrong**

1. **Check GPU memory**: `nvidia-smi`
2. **Check file sizes**: `ls -lh filtered_*.jsonl`
3. **Check logs**: Look for error messages in the terminal
4. **Restart**: Simply re-run the command - it will restart processing

## 🎉 **When Complete**

After filtering, the pipeline will automatically start **Stage 2: Reasoning Generation** using the OpenAI API to create the final training dataset.

---

**Ready to run?** 
```bash
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5 python main.py
```

**Want to monitor?**
```bash
python monitor_progress.py
```
