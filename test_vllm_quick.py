#!/usr/bin/env python3
"""
Quick test for vLLM functionality.
"""

import os
from main import get_filtering_decision

def test_quick():
    """Quick test of filtering functionality."""
    
    test_example = {
        "question": "Which is harder, diamond or wood?",
        "choice_1": "Diamond",
        "choice_2": "Wood",
        "gt": "Diamond"
    }
    
    print("🔥 Quick vLLM test...")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
    
    try:
        result = get_filtering_decision(test_example, "test")
        print(f"✅ Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_quick()
    if success:
        print("🎉 Test passed!")
    else:
        print("💥 Test failed!")
