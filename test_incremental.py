#!/usr/bin/env python3
"""
Test script to verify incremental writing functionality.
"""

import os
import time
from main import SAMPLE_SIZE, filter_datasets, load_original_datasets

def test_incremental_writing():
    """Test incremental writing with a small sample."""
    
    print("🧪 Testing incremental writing functionality...")
    
    # Temporarily set a small sample size for testing
    original_sample_size = SAMPLE_SIZE
    
    # Override sample size for testing
    import main
    main.SAMPLE_SIZE = 5  # Process only 5 examples for testing
    
    try:
        # Load datasets
        print("📦 Loading datasets...")
        raw_datasets = load_original_datasets()
        
        # Pick one dataset for testing
        test_dataset = {"newton_explicit": raw_datasets["newton_explicit"]}
        
        print(f"🔄 Testing with {len(test_dataset['newton_explicit'])} examples...")
        
        # Monitor file creation
        output_file = "filtered_newton_explicit.jsonl"
        if os.path.exists(output_file):
            os.remove(output_file)
        
        print(f"👀 Monitoring file: {output_file}")
        
        # Start filtering
        start_time = time.time()
        filtered_paths = filter_datasets(test_dataset)
        end_time = time.time()
        
        # Check results
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
            
            print(f"✅ File created successfully!")
            print(f"📄 File contains {len(lines)} filtered examples")
            print(f"⏱️  Processing took {end_time - start_time:.2f} seconds")
            
            if lines:
                print(f"📝 First example preview:")
                print(lines[0][:100] + "..." if len(lines[0]) > 100 else lines[0])
            
            return True
        else:
            print(f"❌ Output file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Restore original sample size
        main.SAMPLE_SIZE = original_sample_size

def main():
    print("🚀 Incremental Writing Test")
    print("=" * 50)
    
    success = test_incremental_writing()
    
    if success:
        print("\n🎉 Incremental writing test passed!")
        print("💡 The system will now write results as they're processed")
        print("📊 You can monitor progress in real-time by watching the .jsonl files")
    else:
        print("\n💥 Incremental writing test failed!")

if __name__ == "__main__":
    main()
