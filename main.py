import os
import json
import time
from datasets import load_dataset, Features, Value, Dataset
from openai import OpenAI
from tqdm import tqdm
import dotenv
import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM

# Set environment variables for vLLM to handle GPU detection issues
os.environ["VLLM_WORKER_MULTIPROC_METHOD"] = "spawn"
os.environ["VLLM_LOGGING_LEVEL"] = "WARNING"  # Reduce logging
os.environ["VLLM_DISABLE_CUSTOM_ALL_REDUCE"] = "1"  # Disable custom all-reduce
# Disable problematic NVML initialization
os.environ["VLLM_DISABLE_NVML"] = "1"
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
# Additional environment variables to bypass NVML issues
os.environ["VLLM_SKIP_NVML_INIT"] = "1"
os.environ["VLLM_DISABLE_NVML_WARNINGS"] = "1"

# Try to import vLLM with error handling
try:
    import warnings

    warnings.filterwarnings("ignore", category=UserWarning, module="vllm")
    warnings.filterwarnings("ignore", category=UserWarning, module="pynvml")

    # Try to monkey patch the problematic NVML function
    try:
        import vllm.platforms.cuda

        # Override the problematic log_warnings method
        def dummy_log_warnings(cls):
            pass

        vllm.platforms.cuda.CudaPlatform.log_warnings = classmethod(dummy_log_warnings)
    except:
        pass

    from vllm import LLM, SamplingParams

    VLLM_AVAILABLE = True
    print("✅ vLLM imported successfully!")
except Exception as e:
    print(f"Warning: vLLM import failed: {e}")
    print("Falling back to transformers for local model inference")
    VLLM_AVAILABLE = False
    from transformers import AutoModelForCausalLM

# --- 0. Configuration ---
dotenv.load_dotenv()
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    raise ValueError(
        "OpenAI API key not found. Please set the OPENAI_API_KEY environment variable."
    )

client = OpenAI(api_key=API_KEY)

# Local model configuration for filtering
USE_LOCAL_MODEL_FOR_FILTERING = True  # Set to False to use OpenAI API instead
FILTERING_MODEL_NAME = "meta-llama/Meta-Llama-3.1-8B-Instruct"
filtering_tokenizer = None
filtering_llm = None  # vLLM engine


# Multi-GPU configuration
def get_available_gpus():
    """Get list of available GPU IDs from CUDA_VISIBLE_DEVICES or all available GPUs."""
    cuda_visible = os.environ.get("CUDA_VISIBLE_DEVICES")
    if cuda_visible:
        return [int(x.strip()) for x in cuda_visible.split(",") if x.strip().isdigit()]
    elif torch.cuda.is_available():
        return list(range(torch.cuda.device_count()))
    else:
        return []


AVAILABLE_GPUS = get_available_gpus()
print(f"🔥 Available GPUs: {AVAILABLE_GPUS}")

# Set to None to process all rows, or a small number for testing.
SAMPLE_SIZE = None  # Process all rows (maximum)

# --- 1. Helper Functions ---


def initialize_filtering_model():
    """
    Initialize the local Llama model for filtering decisions using vLLM with multi-GPU support.
    """
    global filtering_tokenizer, filtering_llm

    if filtering_tokenizer is None or filtering_llm is None:
        # Load tokenizer
        filtering_tokenizer = AutoTokenizer.from_pretrained(FILTERING_MODEL_NAME)

        # Check GPU availability
        print(f"CUDA available: {torch.cuda.is_available()}")
        print(f"Available GPUs: {AVAILABLE_GPUS}")

        # Force use of transformers for stability (vLLM has compatibility issues)
        print(f"🔄 Loading filtering model with transformers: {FILTERING_MODEL_NAME}")
        # Use transformers
        filtering_llm = AutoModelForCausalLM.from_pretrained(
            FILTERING_MODEL_NAME,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
        )
        print("✅ Filtering model loaded successfully with transformers!")

        # Check GPU memory usage after loading
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                print(
                    f"GPU {i} memory: {torch.cuda.memory_allocated(i) / 1024**3:.2f} GB allocated"
                )

    return filtering_tokenizer, filtering_llm


def call_local_filtering_model(system_prompt, user_prompt, temperature=0.1):
    """
    Call the local Llama model for filtering decisions with JSON output using vLLM or transformers.
    """
    tokenizer, llm = initialize_filtering_model()

    # Format the conversation for Llama 3.1 chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]

    # Apply chat template
    formatted_prompt = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )

    try:
        # Use transformers for generation (vLLM disabled for compatibility)
        print(f"Model device: {llm.device}")
        inputs = tokenizer(formatted_prompt, return_tensors="pt").to(llm.device)
        print(f"Input device: {inputs['input_ids'].device}")

        # Check GPU memory before generation
        if torch.cuda.is_available():
            print(
                f"GPU memory before generation: {torch.cuda.memory_allocated() / 1024**3:.2f} GB"
            )

        with torch.no_grad():
            outputs = llm.generate(
                **inputs,
                max_new_tokens=512,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )

        # Check GPU memory after generation
        if torch.cuda.is_available():
            print(
                f"GPU memory after generation: {torch.cuda.memory_allocated() / 1024**3:.2f} GB"
            )

        # Decode response
        response = tokenizer.decode(
            outputs[0][inputs["input_ids"].shape[1] :], skip_special_tokens=True
        ).strip()

        # Try to parse JSON response
        try:
            parsed_response = json.loads(response)
            return parsed_response
        except json.JSONDecodeError as json_error:
            print(f"JSON parsing error from local model: {json_error}")
            print(f"Raw response: {response}")
            # Return default structure for filtering
            return {"keep": False, "reason": f"JSON Parse Error: {json_error}"}

    except Exception as e:
        print(f"Local model error: {e}")
        return {"keep": False, "reason": f"Model Error: {e}"}


def load_original_datasets():
    """
    Loads the original datasets with their full schemas from the Hugging Face Hub.
    """
    print("Loading original datasets from Hugging Face Hub...")

    # Define schemas to ensure correct data loading
    newton_explicit_features = Features(
        {
            "q_type": Value("string"),
            "attribute": Value("string"),
            "category": Value("string"),
            "question": Value("string"),
            "polarity": Value("string"),
            "gt": Value("string"),
            "choice_1": Value("string"),
            "choice_2": Value("string"),
            "choice_3": Value("string"),
            "choice_4": Value("string"),
        }
    )

    newton_implicit_features = Features(
        {
            "q_type": Value("string"),
            "tag": Value("string"),
            "context": Value("string"),
            "question": Value("string"),
            "gt": Value("string"),
            "choice_1": Value("string"),
            "choice_2": Value("string"),
            "choice_3": Value("string"),
            "choice_4": Value("string"),
        }
    )

    datasets = {
        "newton_explicit": load_dataset(
            "NEWTONReasoning/NEWTON",
            data_files="explicit_questions.csv",
            features=newton_explicit_features,
            split="train",
        ),
        "newton_implicit": load_dataset(
            "NEWTONReasoning/NEWTON",
            data_files="implicit_questions.csv",
            features=newton_implicit_features,
            split="train",
        ),
        "physical_reasoning": load_dataset(
            "grimulkan/physical-reasoning", split="train"
        ),
        "prost": load_dataset("corypaik/prost", split="test"),
        "piqa": load_dataset("ybisk/piqa", split="train"),
    }
    print("All datasets loaded successfully.")
    return datasets


def call_openai_api(
    system_prompt, user_prompt, model="gpt-4o-mini", temperature=0.1, is_json=False
):
    """
    A generic helper function to interact with the OpenAI API and handle errors.
    """
    try:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        response_format = {"type": "json_object"} if is_json else {"type": "text"}

        completion = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            response_format=response_format,
        )
        content = completion.choices[0].message.content

        if is_json:
            try:
                parsed_content = json.loads(content)
                return parsed_content
            except json.JSONDecodeError as json_error:
                print(f"JSON parsing error: {json_error}")
                print(f"Raw content: {content}")
                # Return a default structure for different contexts
                if "keep" in system_prompt.lower():
                    return {"keep": False, "reason": f"JSON Parse Error: {json_error}"}
                else:
                    return {
                        "question": "Error parsing JSON",
                        "answer": "Error parsing JSON",
                    }
        else:
            return content

    except Exception as e:
        print(f"An API error occurred: {e}")
        time.sleep(5)  # Wait before retrying
        # Return a structure that won't break the calling function
        if is_json:
            if "keep" in system_prompt.lower():
                return {"keep": False, "reason": f"API Error: {e}"}
            else:
                return {"question": "API Error", "answer": "API Error"}
        return "Error in generation."


# --- 2. Stage 1: AI-Powered Filtering ---


def get_filtering_decision(example, dataset_name):
    """
    Calls the AI model (local or API) to get a keep/discard decision for a single data row.
    """
    # Convert the LazyRow or LazyDict to a standard Python dict to avoid JSON errors
    example_dict = dict(example)

    # --- UPDATED, MORE LENIENT PROMPT ---
    system_prompt = """
You are a Lead Data Scientist responsible for curating a dataset to teach an AI physical reasoning. Your goal is to create a high-quality training set that covers diverse physical concepts while avoiding excessive repetition.

You will be given a data point. Your task is to judge its value for our training set.

**Evaluation Criteria:**

1.  **High Value (Definitely Keep):**
    * The question involves physical reasoning, spatial relationships, or material properties.
    * It tests understanding of physics concepts like gravity, buoyancy, hardness, motion, etc.
    * The question is clear and has educational value.

2.  **Medium Value (Keep):**
    * Simple questions that test basic physical concepts (e.g., "Is X harder than Y?").
    * Questions about object properties, spatial arrangements, or cause-and-effect relationships.
    * Even templated questions are valuable if they test different physical concepts or object combinations.

3.  **Low Value (Consider Discarding):**
    * Questions that are completely nonsensical or have no physical basis.
    * Questions with unclear or contradictory information.
    * Exact duplicates of previously seen questions.

**Approach:**
Be generous in keeping questions that have any educational value for physical reasoning. Simple questions are often good building blocks for learning. Only discard questions that are truly problematic or completely nonsensical.

Your output **MUST** be a JSON object with two keys:
1. "keep": A boolean (true or false).
2. "reason": A brief, one-sentence justification for your decision (e.g., "Keep: Tests hardness comparison concept.", "Keep: Good spatial reasoning question.", "Discard: Nonsensical question.").
"""

    # Create a user prompt specific to the dataset's structure
    user_prompt = f"Dataset: {dataset_name}\nData:\n"
    user_prompt += json.dumps(example_dict, indent=2)
    user_prompt += "\n\nEvaluate this data point based on the criteria. Should I keep it for my training set?"

    # Choose between local model and API based on configuration
    if USE_LOCAL_MODEL_FOR_FILTERING:
        decision = call_local_filtering_model(system_prompt, user_prompt)
    else:
        decision = call_openai_api(system_prompt, user_prompt, is_json=True)

    return {
        "keep": decision.get("keep", False),
        "reason": decision.get("reason", "Invalid JSON response"),
    }


def filter_datasets(raw_datasets):
    """
    Orchestrates the filtering stage. Iterates through datasets, gets AI decisions,
    filters, and saves the curated data to local files.
    """
    print("\n--- Starting Stage 1: AI-Powered Filtering ---")
    if USE_LOCAL_MODEL_FOR_FILTERING:
        backend = "vLLM" if VLLM_AVAILABLE else "transformers"
        print(
            f"Using local model for filtering: {FILTERING_MODEL_NAME} (backend: {backend})"
        )
        if VLLM_AVAILABLE:
            print(f"🚀 vLLM will process with optimized batching for maximum speed!")
    else:
        print("Using OpenAI API for filtering")
    filtered_data_paths = {}

    for name, ds in raw_datasets.items():
        print(f"\n🔥 Filtering dataset: {name}...")

        # Apply sample size for testing
        if SAMPLE_SIZE:
            ds = ds.select(range(min(SAMPLE_SIZE, len(ds))))

        print(f"📊 Processing {len(ds)} examples...")

        # Get AI decision for each row with optimized processing
        ds_with_decisions = ds.map(
            lambda example: get_filtering_decision(example, name),
            num_proc=1,  # Single process to avoid model loading conflicts
        )

        # Filter the dataset based on the 'keep' flag
        filtered_ds = ds_with_decisions.filter(lambda example: example["keep"])

        # Remove the temporary decision columns
        filtered_ds = filtered_ds.remove_columns(["keep", "reason"])

        # Save the filtered dataset to a local file
        output_filename = f"filtered_{name}.jsonl"
        filtered_ds.to_json(output_filename, orient="records", lines=True)
        filtered_data_paths[name] = output_filename

        original_count = len(ds)
        kept_count = len(filtered_ds)
        print(
            f"✅ Finished filtering {name}. Kept {kept_count}/{original_count} samples."
        )
        print(f"💾 Saved curated data to: {output_filename}")

    return filtered_data_paths


# --- 3. Stage 2: Reasoning Generation ---


def generate_reasoning_datasets(filtered_data_paths):
    """
    Orchestrates the generation stage. Loads filtered data and creates the
    final (question, solution, answer) triplets.
    """
    print("\n--- Starting Stage 2: Reasoning Generation ---")

    def transform_example(example, dataset_name):
        example_dict = dict(example)

        # Enhanced system prompt for question and answer generation
        sys_prompt_q_a = """You are a physics expert creating educational material. Your task is to reframe the given data into a practical reasoning question and a concise answer.

The output must be a valid JSON object with exactly two keys:
- "question": A clear, well-formed question based on the data
- "answer": A direct, concise answer to the question

Ensure the JSON is properly formatted and contains both required keys."""

        user_prompt_q_a = f"Based on the following data from the '{dataset_name}' dataset, create a new, high-quality reasoning question and a short, direct answer.\n\nData:\n{json.dumps(example_dict, indent=2)}\n\nReturn only the JSON object with 'question' and 'answer' keys."

        # Try to get question and answer with better error handling
        qa_result = call_openai_api(sys_prompt_q_a, user_prompt_q_a, is_json=True)

        # Ensure we have valid question and answer
        question = qa_result.get("question", "Error generating question")
        answer = qa_result.get("answer", "Error generating answer")

        # If the API call failed, try to extract from the original data
        if (
            question == "Error generating question"
            or not question
            or question == "Error"
        ):
            if "question" in example_dict:
                question = example_dict["question"]
            elif "instruction" in example_dict:
                question = example_dict["instruction"]
            elif "goal" in example_dict:
                question = example_dict["goal"]
            else:
                question = f"Question based on {dataset_name} data"

        if answer == "Error generating answer" or not answer or answer == "Error":
            if "gt" in example_dict:
                answer = example_dict["gt"]
            elif "response" in example_dict:
                answer = example_dict["response"]
            else:
                answer = "Answer not available"

        sys_prompt_sol = "You are a physics teacher. Your task is to provide a clear, step-by-step reasoning process (a 'solution') that explains how to arrive at the answer for the given question."
        user_prompt_sol = f"Question: {question}\n\nAnswer: {answer}\n\nPlease provide a detailed, thought-process-style solution that explains the physical principles leading to the answer. The original data context was:\n{json.dumps(example_dict, indent=2)}"
        solution = call_openai_api(sys_prompt_sol, user_prompt_sol)

        return {
            "question": question,
            "answer": answer,
            "solution": solution,
        }

    for name, path in filtered_data_paths.items():
        print(f"\nGenerating reasoning for dataset: {name} from file {path}...")

        if not os.path.exists(path) or os.path.getsize(path) == 0:
            print(
                f"Skipping generation for {name} because the filtered file is empty or does not exist."
            )
            continue

        filtered_ds = Dataset.from_json(path)
        final_ds = filtered_ds.map(lambda example: transform_example(example, name))
        # Only keep the question, answer, and solution columns
        final_ds = final_ds.select_columns(["question", "answer", "solution"])
        output_filename = f"final_reasoning_{name}.jsonl"
        final_ds.to_json(output_filename, orient="records", lines=True)

        print(f"Finished generation for {name}. Saved final data to: {output_filename}")
        if len(final_ds) > 0:
            print("Sample of final data:")
            print(final_ds[0])


# --- 4. Main Execution ---


def main():
    """Main function to orchestrate the entire pipeline."""
    raw_datasets = load_original_datasets()
    filtered_paths = filter_datasets(raw_datasets)
    generate_reasoning_datasets(filtered_paths)
    print("\n--- Pipeline Complete! ---")


if __name__ == "__main__":
    main()
