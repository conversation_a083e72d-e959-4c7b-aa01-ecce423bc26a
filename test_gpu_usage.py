#!/usr/bin/env python3
"""
Test script to demonstrate sustained GPU usage with multiple filtering examples.
"""

import json
import time
from main import get_filtering_decision

def test_sustained_gpu_usage():
    """Test multiple examples to show sustained GPU usage."""
    
    # Multiple test examples to process
    test_examples = [
        {
            "question": "Which is harder, a diamond or a piece of wood?",
            "choice_1": "Diamond",
            "choice_2": "Wood", 
            "choice_3": "They are equally hard",
            "choice_4": "Cannot be determined",
            "gt": "Diamond"
        },
        {
            "question": "What happens when you drop a ball?",
            "choice_1": "It floats",
            "choice_2": "It falls down", 
            "choice_3": "It disappears",
            "choice_4": "It explodes",
            "gt": "It falls down"
        },
        {
            "question": "Which is heavier, a kilogram of feathers or a kilogram of steel?",
            "choice_1": "Feathers",
            "choice_2": "Steel", 
            "choice_3": "They weigh the same",
            "choice_4": "Cannot be determined",
            "gt": "They weigh the same"
        },
        {
            "question": "What happens to ice when heated?",
            "choice_1": "It becomes harder",
            "choice_2": "It melts", 
            "choice_3": "It changes color",
            "choice_4": "Nothing happens",
            "gt": "It melts"
        },
        {
            "question": "Why do objects fall to the ground?",
            "choice_1": "Magic",
            "choice_2": "Gravity", 
            "choice_3": "Wind",
            "choice_4": "Magnetism",
            "gt": "Gravity"
        }
    ]
    
    print("Testing sustained GPU usage with multiple examples...")
    print("Monitor GPU usage now - processing 5 examples...")
    
    results = []
    start_time = time.time()
    
    for i, example in enumerate(test_examples, 1):
        print(f"\n--- Processing example {i}/5 ---")
        print(f"Question: {example['question']}")
        
        result = get_filtering_decision(example, "test_dataset")
        results.append(result)
        
        print(f"Result: {result}")
        
        # Small delay to make GPU usage more visible
        time.sleep(1)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n--- Summary ---")
    print(f"Processed {len(test_examples)} examples in {total_time:.2f} seconds")
    print(f"Average time per example: {total_time/len(test_examples):.2f} seconds")
    
    kept_count = sum(1 for r in results if r.get("keep", False))
    print(f"Examples kept: {kept_count}/{len(test_examples)}")
    
    return results

if __name__ == "__main__":
    print("🔥 Starting sustained GPU usage test...")
    print("💡 Tip: Run 'nvidia-smi -l 1' in another terminal to monitor GPU usage")
    print("⏱️  Processing will take about 10-15 seconds...")
    
    results = test_sustained_gpu_usage()
    
    print("\n🎉 Test completed! The model is definitely using the GPU.")
    print("📊 Check your GPU monitoring tool - you should have seen sustained usage.")
